// Example showing how to use the new AdBlockWebView widget
// This demonstrates how to replace existing InAppWebView usage with AdBlockWebView

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import '../widgets/ad_block_webview.dart';

class WebViewUsageExample extends StatefulWidget {
  final String channelUrl;
  final InAppWebViewSettings? initialWebViewSettings;

  const WebViewUsageExample({
    Key? key,
    required this.channelUrl,
    this.initialWebViewSettings,
  }) : super(key: key);

  @override
  _WebViewUsageExampleState createState() => _WebViewUsageExampleState();
}

class _WebViewUsageExampleState extends State<WebViewUsageExample> {
  InAppWebViewController? _webViewController;
  bool _shouldBlockAds = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('WebView Example'),
        actions: [
          IconButton(
            icon: Icon(
              _shouldBlockAds ? Icons.shield : Icons.shield_outlined,
              color: Colors.white,
            ),
            onPressed: () {
              setState(() {
                _shouldBlockAds = !_shouldBlockAds;
              });
            },
            tooltip: _shouldBlockAds ? 'Disable Ad Blocking' : 'Enable Ad Blocking',
          ),
        ],
      ),
      body: SafeArea(
        child: Center(
          // BEFORE: Using InAppWebView directly
          // child: InAppWebView(
          //   initialUrlRequest: URLRequest(url: WebUri(widget.channelUrl)),
          //   initialSettings: widget.initialWebViewSettings ?? InAppWebViewSettings(...),
          //   onWebViewCreated: (controller) {
          //     _webViewController = controller;
          //   },
          //   ...
          // ),

          // AFTER: Using AdBlockWebView with enhanced features
          child: AdBlockWebView(
            initialUrl: widget.channelUrl,
            enableAdBlocking: _shouldBlockAds,
            initialSettings: widget.initialWebViewSettings,
            onWebViewCreated: (controller) {
              _webViewController = controller;
            },
            onLoadStart: (controller, url) {
              debugPrint("WebView started loading: $url");
            },
            onLoadStop: (controller, url) {
              debugPrint("WebView finished loading: $url");
            },
            onLoadError: (controller, error) {
              debugPrint("WebView error: ${error.description}");
            },
            onConsoleMessage: (controller, message) {
              debugPrint("WebView Console: ${message.message}");
            },
            shouldOverrideUrlLoading: (controller, navigationAction) async {
              final url = navigationAction.request.url.toString();
              
              // Example: Block certain domains
              if (url.contains('unwanted-domain.com')) {
                return NavigationActionPolicy.CANCEL;
              }
              
              return NavigationActionPolicy.ALLOW;
            },
          ),
        ),
      ),
    );
  }
}

// Example for headless usage (like in fixtures scraping)
class HeadlessWebViewExample extends StatefulWidget {
  final String url;

  const HeadlessWebViewExample({Key? key, required this.url}) : super(key: key);

  @override
  _HeadlessWebViewExampleState createState() => _HeadlessWebViewExampleState();
}

class _HeadlessWebViewExampleState extends State<HeadlessWebViewExample> {
  String _statusMessage = 'Initializing...';

  @override
  void initState() {
    super.initState();
    _startHeadlessScraping();
  }

  void _startHeadlessScraping() {
    setState(() {
      _statusMessage = 'Starting headless scraping...';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Headless WebView Example')),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              _statusMessage,
              style: const TextStyle(fontSize: 16),
            ),
          ),
          Expanded(
            child: AdBlockWebView(
              initialUrl: widget.url,
              headless: true, // This makes it invisible
              enableAdBlocking: true,
              onWebViewCreated: (controller) {
                debugPrint('Headless WebView created');
              },
              onLoadStart: (controller, url) {
                setState(() {
                  _statusMessage = 'Loading: $url';
                });
              },
              onLoadStop: (controller, url) async {
                setState(() {
                  _statusMessage = 'Finished loading: $url';
                });
                
                // Execute scraping script
                final result = await controller.evaluateJavascript(
                  source: '''
                    // Your scraping JavaScript here
                    document.title;
                  ''',
                );
                
                debugPrint('Scraping result: $result');
              },
              onLoadError: (controller, error) {
                setState(() {
                  _statusMessage = 'Error: ${error.description}';
                });
              },
            ),
          ),
        ],
      ),
    );
  }
}

// Key Benefits of AdBlockWebView:
// 
// 1. Built-in EasyList ad blocking with ContentBlocker
// 2. Automatic injection of additional ad blocking scripts
// 3. Headless mode support for background scraping
// 4. Consistent settings and behavior across the app
// 5. Easy to toggle ad blocking on/off
// 6. Better performance due to blocked network requests
// 7. Cleaner, more maintainable code
//
// Usage Tips:
// - Set enableAdBlocking: false for pages where you want ads
// - Use headless: true for background data scraping
// - The widget automatically loads EasyList filters on initialization
// - Console messages can help track blocked ads and requests
// - All existing InAppWebView callbacks are supported
