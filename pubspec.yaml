name: cat_tv
description: 'A Flutter app for entertaining cats.'
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  sqflite: ^2.3.0 # Added for SQLite database
  sqflite_common_ffi: ^2.3.0 # Added for FFI support on desktop
  path_provider: ^2.1.5 # Added for finding the correct local path
  http: ^1.4.0
  html: ^0.15.0 # Added for HTML parsing
  url_launcher: ^6.0.0 # Added for launching URLs
  csv: ^6.0.0 # Added for CSV parsing
  path: ^1.9.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  ffi: ^2.1.4
  flutter_inappwebview: ^6.0.0
  media_kit: ^1.2.0
  media_kit_video: ^1.2.0
  media_kit_libs_video: ^1.0.5 # Added for native video dependencies
  media_kit_libs_windows_video: any
  flutter_hls_parser: 2.0.1
  country_flags: ^2.0.1 # Added for displaying country flags
  package_info_plus: ^8.0.0
  flutter_gen: ^5.10.0
  provider: ^6.0.5 # Added for state management
  mixpanel_flutter: ^2.1.1 # For Android/iOS (optional on Windows)
  win32: ^5.13.0
  shimmer: ^3.0.0 # Added for skeleton loading
  shared_preferences: ^2.0.0 # Added for ad management
  encrypt: ^5.0.1 # Added for encryption
  cached_network_image: ^3.3.1 # Added for caching network images
  logger: ^2.6.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.0.0 # Added for mocking
  build_runner: ^2.0.0 # Added for mock generation

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0 # Updated to a more recent version, check pub.dev for latest

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true # Enable Flutter's localization generation
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #       fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  assets:
    - assets/db/app.db
    - assets/logo/
    - assets/ads/
