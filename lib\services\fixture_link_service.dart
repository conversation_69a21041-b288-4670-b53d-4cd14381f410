import 'package:flutter/foundation.dart';
import 'package:cat_tv/services/site_update_service.dart'; // Import SiteUpdateService

class FixtureLinkService {
  Future<void> openFixtureLink({
    required String sportType,
    required ValueSetter<bool> onOpening,
    required ValueSetter<String> onStatusUpdate,
  }) async {
    onOpening(true);
    onStatusUpdate('Preparing to scrape...');
  }

  String getScrapingUrl(String sportType) {
    String baseUrl = SiteUpdateService.GOOGLE_SITE;
    if (!baseUrl.endsWith('/')) {
      baseUrl += '/';
    }
    return '$baseUrl${sportType.toLowerCase()}.html';
  }
}
