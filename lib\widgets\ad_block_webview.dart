import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import '../services/webview_utils.dart';

class AdBlockWebView extends StatefulWidget {
  final String initialUrl;
  final bool headless;
  final Function(InAppWebViewController)? onWebViewCreated;
  final Function(InAppWebViewController, WebUri?)? onLoadStart;
  final Function(InAppWebViewController, WebUri?)? onLoadStop;
  final Function(InAppWebViewController, WebResourceError)? onLoadError;
  final Function(InAppWebViewController, ConsoleMessage)? onConsoleMessage;
  final Function(InAppWebViewController, NavigationAction)?
  shouldOverrideUrlLoading;
  final InAppWebViewSettings? initialSettings;
  final bool enableAdBlocking;

  const AdBlockWebView({
    super.key,
    required this.initialUrl,
    this.headless = false,
    this.onWebViewCreated,
    this.onLoadStart,
    this.onLoadStop,
    this.onLoadError,
    this.onConsoleMessage,
    this.shouldOverrideUrlLoading,
    this.initialSettings,
    this.enableAdBlocking = true,
  });

  @override
  _AdBlockWebViewState createState() => _AdBlockWebViewState();
}

class _AdBlockWebViewState extends State<AdBlockWebView> {
  final GlobalKey _webViewKey = GlobalKey();
  InAppWebViewController? _webViewController;
  List<ContentBlocker> _contentBlockers = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    if (widget.enableAdBlocking) {
      _initializeContentBlockers();
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _initializeContentBlockers() async {
    try {
      _contentBlockers = await WebViewUtils.loadEasyListFilters();
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error initializing content blockers: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> reload() async {
    await _webViewController?.reload();
  }

  Future<void> loadUrl(String url) async {
    await _webViewController?.loadUrl(urlRequest: URLRequest(url: WebUri(url)));
  }

  InAppWebViewSettings _getWebViewSettings() {
    final defaultSettings = InAppWebViewSettings(
      javaScriptEnabled: true,
      mediaPlaybackRequiresUserGesture: false,
      domStorageEnabled: true,
      databaseEnabled: true,
      javaScriptCanOpenWindowsAutomatically: false,
      supportMultipleWindows: false,
      allowsInlineMediaPlayback: true,
      allowsBackForwardNavigationGestures: false,
      allowsAirPlayForMediaPlayback: false,
      allowFileAccessFromFileURLs: false,
      allowUniversalAccessFromFileURLs: false,
      mixedContentMode: MixedContentMode.MIXED_CONTENT_NEVER_ALLOW,
      contentBlockers: widget.enableAdBlocking ? _contentBlockers : [],
    );

    // Merge with custom settings if provided
    if (widget.initialSettings != null) {
      // Create a new settings object with content blockers
      return InAppWebViewSettings(
        javaScriptEnabled: widget.initialSettings!.javaScriptEnabled,
        mediaPlaybackRequiresUserGesture:
            widget.initialSettings!.mediaPlaybackRequiresUserGesture,
        domStorageEnabled: widget.initialSettings!.domStorageEnabled,
        databaseEnabled: widget.initialSettings!.databaseEnabled,
        javaScriptCanOpenWindowsAutomatically:
            widget.initialSettings!.javaScriptCanOpenWindowsAutomatically,
        supportMultipleWindows: widget.initialSettings!.supportMultipleWindows,
        allowsInlineMediaPlayback:
            widget.initialSettings!.allowsInlineMediaPlayback,
        allowsBackForwardNavigationGestures:
            widget.initialSettings!.allowsBackForwardNavigationGestures,
        allowsAirPlayForMediaPlayback:
            widget.initialSettings!.allowsAirPlayForMediaPlayback,
        allowFileAccessFromFileURLs:
            widget.initialSettings!.allowFileAccessFromFileURLs,
        allowUniversalAccessFromFileURLs:
            widget.initialSettings!.allowUniversalAccessFromFileURLs,
        mixedContentMode: widget.initialSettings!.mixedContentMode,
        contentBlockers: widget.enableAdBlocking ? _contentBlockers : [],
      );
    }

    return defaultSettings;
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (widget.headless) {
      // For headless mode, we need to return a widget but the webview is invisible
      HeadlessInAppWebView(
        initialUrlRequest: URLRequest(url: WebUri(widget.initialUrl)),
        initialSettings: _getWebViewSettings(),
        onWebViewCreated: (controller) {
          _webViewController = controller;
          widget.onWebViewCreated?.call(controller);
        },
        onLoadStart: (controller, url) {
          widget.onLoadStart?.call(controller, url);
        },
        onLoadStop: (controller, url) async {
          // Inject additional ad blocking script
          if (widget.enableAdBlocking) {
            await WebViewUtils.injectAdBlockingScript(controller);
          }
          widget.onLoadStop?.call(controller, url);
        },
        onReceivedError: (controller, request, error) {
          widget.onLoadError?.call(controller, error);
        },
        onConsoleMessage: (controller, message) {
          widget.onConsoleMessage?.call(controller, message);
        },
        shouldOverrideUrlLoading: (controller, navigationAction) async {
          if (widget.shouldOverrideUrlLoading != null) {
            return await widget.shouldOverrideUrlLoading!(
              controller,
              navigationAction,
            );
          }
          return NavigationActionPolicy.ALLOW;
        },
      );

      // Return an empty container for headless mode
      return const SizedBox.shrink();
    }

    return InAppWebView(
      key: _webViewKey,
      initialUrlRequest: URLRequest(url: WebUri(widget.initialUrl)),
      initialSettings: _getWebViewSettings(),
      onWebViewCreated: (controller) {
        _webViewController = controller;
        widget.onWebViewCreated?.call(controller);
      },
      onLoadStart: (controller, url) {
        widget.onLoadStart?.call(controller, url);
      },
      onLoadStop: (controller, url) async {
        // Inject additional ad blocking script
        if (widget.enableAdBlocking) {
          await WebViewUtils.injectAdBlockingScript(controller);
        }
        widget.onLoadStop?.call(controller, url);
      },
      onReceivedError: (controller, request, error) {
        widget.onLoadError?.call(controller, error);
      },
      onConsoleMessage: (controller, message) {
        widget.onConsoleMessage?.call(controller, message);
      },
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        if (widget.shouldOverrideUrlLoading != null) {
          return await widget.shouldOverrideUrlLoading!(
            controller,
            navigationAction,
          );
        }
        return NavigationActionPolicy.ALLOW;
      },
    );
  }
}
