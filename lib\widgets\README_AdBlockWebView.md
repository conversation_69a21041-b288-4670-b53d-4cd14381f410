# AdBlockWebView Widget

A reusable Flutter widget that provides enhanced WebView functionality with built-in ad blocking capabilities using EasyList filters.

## Features

- **Built-in Ad Blocking**: Uses EasyList filters with ContentBlocker API
- **Headless Mode**: Support for invisible background WebViews
- **Easy Integration**: Drop-in replacement for InAppWebView
- **Customizable Settings**: All InAppWebView settings supported
- **Performance Optimized**: Blocks ads at network level
- **Cross-Platform**: Works on Android, iOS, Windows, macOS

## Usage

### Basic Usage

```dart
import 'package:cat_tv/widgets/ad_block_webview.dart';

AdBlockWebView(
  initialUrl: 'https://example.com',
  enableAdBlocking: true,
  onWebViewCreated: (controller) {
    // WebView created
  },
  onLoadStop: (controller, url) {
    // Page loaded
  },
)
```

### Headless Mode (for scraping)

```dart
AdBlockWebView(
  initialUrl: 'https://example.com',
  headless: true,
  enableAdBlocking: true,
  onLoadStop: (controller, url) async {
    // Execute scraping script
    final result = await controller.evaluateJavascript(
      source: 'document.title',
    );
  },
)
```

### With Custom Settings

```dart
AdBlockWebView(
  initialUrl: 'https://example.com',
  enableAdBlocking: true,
  initialSettings: InAppWebViewSettings(
    javaScriptEnabled: true,
    domStorageEnabled: true,
    // ... other settings
  ),
)
```

### Disable Ad Blocking

```dart
AdBlockWebView(
  initialUrl: 'https://example.com',
  enableAdBlocking: false, // Disable ad blocking
)
```

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `initialUrl` | `String` | required | Initial URL to load |
| `headless` | `bool` | `false` | Enable headless mode |
| `enableAdBlocking` | `bool` | `true` | Enable/disable ad blocking |
| `initialSettings` | `InAppWebViewSettings?` | `null` | Custom WebView settings |
| `onWebViewCreated` | `Function?` | `null` | WebView created callback |
| `onLoadStart` | `Function?` | `null` | Load start callback |
| `onLoadStop` | `Function?` | `null` | Load stop callback |
| `onLoadError` | `Function?` | `null` | Load error callback |
| `onConsoleMessage` | `Function?` | `null` | Console message callback |
| `shouldOverrideUrlLoading` | `Function?` | `null` | URL override callback |

## Ad Blocking Details

The widget uses two layers of ad blocking:

1. **ContentBlocker API**: Blocks ads at network level using EasyList filters
2. **JavaScript Injection**: Additional script-based blocking for dynamic content

### EasyList Integration

- Loads filters from `assets/ads/easylist.txt`
- Converts EasyList syntax to ContentBlocker format
- Limits to 1000 rules for performance
- Includes generic CSS element hiding

### Blocked Content Types

- Images from ad networks
- JavaScript from ad servers
- Common ad elements (CSS selectors)
- Aggressive redirects and popups

## Test Page

A test page is included to verify ad blocking functionality:

```dart
import 'package:cat_tv/pages/webview_test_page.dart';

// Add to your navigation
const WebViewTestPage()
```

The test page includes:
- URL input field
- Quick test buttons for ad-heavy sites
- Ad blocking toggle
- Blocked ads counter
- Loading status indicator

## Migration from InAppWebView

Replace your existing InAppWebView with AdBlockWebView:

```dart
// Before
InAppWebView(
  initialUrlRequest: URLRequest(url: WebUri(url)),
  initialSettings: settings,
  onWebViewCreated: (controller) { ... },
)

// After
AdBlockWebView(
  initialUrl: url,
  initialSettings: settings,
  enableAdBlocking: true,
  onWebViewCreated: (controller) { ... },
)
```

## Performance Benefits

- Faster page loading (blocked network requests)
- Reduced data usage
- Better user experience
- Cleaner page content
- Improved privacy

## Troubleshooting

### Ad blocking not working
- Ensure `enableAdBlocking: true`
- Check console messages for blocked requests
- Verify EasyList file exists in assets

### Headless mode not working
- Set `headless: true`
- Widget returns `SizedBox.shrink()` for invisible UI
- Use callbacks to handle WebView events

### Performance issues
- Reduce EasyList rules limit in `WebViewUtils.loadEasyListFilters()`
- Disable ad blocking for specific pages if needed
- Use headless mode for background tasks

## Dependencies

- `flutter_inappwebview: ^6.0.0`
- EasyList filters in `assets/ads/easylist.txt`
