import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import '../widgets/ad_block_webview.dart';

class WebViewTestPage extends StatefulWidget {
  const WebViewTestPage({Key? key}) : super(key: key);

  @override
  _WebViewTestPageState createState() => _WebViewTestPageState();
}

class _WebViewTestPageState extends State<WebViewTestPage> {
  final TextEditingController _urlController = TextEditingController(
    text: 'https://www.tomshardware.com/',
  );
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  InAppWebViewController? _webViewController;
  bool _isLoading = false;
  bool _adBlockingEnabled = true;
  String _currentUrl = '';
  int _blockedAdsCount = 0;

  final List<String> _testUrls = [
    'https://www.tomshardware.com/',
    'https://www.cnn.com/',
    'https://www.forbes.com/',
    'https://www.theverge.com/',
    'https://www.techcrunch.com/',
  ];

  @override
  void initState() {
    super.initState();
    _currentUrl = _urlController.text;
  }

  void _loadUrl() {
    final url = _urlController.text.trim();
    if (url.isNotEmpty && _webViewController != null) {
      _webViewController!.loadUrl(urlRequest: URLRequest(url: WebUri(url)));
    }
  }

  void _loadTestUrl(String url) {
    _urlController.text = url;
    _loadUrl();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'AdBlock WebView Tester',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.black.withValues(alpha: 0.8),
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _adBlockingEnabled ? Icons.shield : Icons.shield_outlined,
              color: _adBlockingEnabled ? Colors.green : Colors.grey,
            ),
            onPressed: () {
              setState(() {
                _adBlockingEnabled = !_adBlockingEnabled;
                _blockedAdsCount = 0;
              });
              // Reload the page with new ad blocking setting
              _loadUrl();
            },
            tooltip:
                _adBlockingEnabled
                    ? 'Disable Ad Blocking'
                    : 'Enable Ad Blocking',
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadUrl,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color.fromARGB(255, 20, 20, 20),
              Color.fromARGB(255, 50, 0, 50),
            ],
          ),
        ),
        child: Column(
          children: [
            // URL Input Section
            Container(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _urlController,
                          style: const TextStyle(color: Colors.white),
                          decoration: InputDecoration(
                            labelText: 'Enter URL',
                            labelStyle: const TextStyle(color: Colors.white70),
                            border: OutlineInputBorder(
                              borderSide: const BorderSide(
                                color: Colors.white30,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderSide: const BorderSide(
                                color: Colors.white30,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderSide: const BorderSide(
                                color: Colors.deepPurpleAccent,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            filled: true,
                            fillColor: Colors.white.withValues(alpha: 0.1),
                          ),
                          keyboardType: TextInputType.url,
                          onSubmitted: (_) => _loadUrl(),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: _loadUrl,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.deepPurpleAccent,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 16,
                          ),
                        ),
                        child: const Text('LOAD'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Test URLs
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children:
                          _testUrls.map((url) {
                            final domain = Uri.parse(url).host;
                            return Padding(
                              padding: const EdgeInsets.only(right: 8.0),
                              child: ElevatedButton(
                                onPressed: () => _loadTestUrl(url),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.white.withValues(
                                    alpha: 0.1,
                                  ),
                                  foregroundColor: Colors.white,
                                  side: const BorderSide(color: Colors.white30),
                                ),
                                child: Text(domain),
                              ),
                            );
                          }).toList(),
                    ),
                  ),
                ],
              ),
            ),
            // Status Bar
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: Colors.black.withValues(alpha: 0.3),
              child: Row(
                children: [
                  Icon(
                    _isLoading ? Icons.hourglass_empty : Icons.check_circle,
                    color: _isLoading ? Colors.orange : Colors.green,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _isLoading ? 'Loading...' : _currentUrl,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (_adBlockingEnabled) ...[
                    const SizedBox(width: 16),
                    Icon(Icons.shield, color: Colors.green, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      'Blocked: $_blockedAdsCount',
                      style: const TextStyle(color: Colors.green, fontSize: 12),
                    ),
                  ],
                ],
              ),
            ),
            const Divider(height: 1, color: Colors.white30),
            // WebView
            Expanded(
              child: AdBlockWebView(
                key: ValueKey('${_urlController.text}_$_adBlockingEnabled'),
                initialUrl: _urlController.text,
                enableAdBlocking: _adBlockingEnabled,
                onWebViewCreated: (controller) {
                  _webViewController = controller;
                },
                onLoadStart: (controller, url) {
                  setState(() {
                    _isLoading = true;
                    _currentUrl = url?.toString() ?? '';
                  });
                },
                onLoadStop: (controller, url) {
                  setState(() {
                    _isLoading = false;
                    _currentUrl = url?.toString() ?? '';
                  });
                },
                onLoadError: (controller, error) {
                  setState(() {
                    _isLoading = false;
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error loading page: ${error.description}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
                onConsoleMessage: (controller, message) {
                  debugPrint('WebView Console: ${message.message}');
                  // Count blocked ads based on console messages
                  if (message.message.toLowerCase().contains('blocked') ||
                      message.message.toLowerCase().contains('ad blocking')) {
                    setState(() {
                      _blockedAdsCount++;
                    });
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }
}
